name: 'Publish Release'
description: 'Builds, prepares, and publishes the gemini-cli packages to npm and creates a GitHub release.'

inputs:
  release-version:
    description: 'The version to release (e.g., 0.1.11).'
    required: true
  npm-tag:
    description: 'The npm tag to publish with (e.g., latest, preview, nightly).'
    required: true
  wombat-token-core:
    description: 'The npm token for the @google/gemini-cli-core package.'
    required: true
  wombat-token-cli:
    description: 'The npm token for the @google/gemini-cli package.'
    required: true
  github-token:
    description: 'The GitHub token for creating the release.'
    required: true
  dry-run:
    description: 'Whether to run in dry-run mode.'
    type: 'string'
    required: true
  release-tag:
    description: 'The release tag for the release (e.g., v0.1.11).'
    required: true
  previous-tag:
    description: 'The previous tag to use for generating release notes.'
    required: true
  skip-github-release:
    description: 'Whether to skip creating a GitHub release.'
    type: 'boolean'
    required: false
    default: false
  working-directory:
    description: 'The working directory to run the steps in.'
    required: false
    default: '.'

runs:
  using: 'composite'
  steps:
    - name: '📝 Print Inputs'
      shell: 'bash'
      run: |
        echo "${{ toJSON(inputs) }}"

    - name: '👤 Configure Git User'
      working-directory: '${{ inputs.working-directory }}'
      shell: 'bash'
      run: |
        git config user.name "gemini-cli-robot"
        git config user.email "<EMAIL>"

    - name: '🌿 Create and switch to a release branch'
      working-directory: '${{ inputs.working-directory }}'
      id: 'release_branch'
      shell: 'bash'
      run: |
        BRANCH_NAME="release/${{ inputs.release-tag }}"
        git switch -c "${BRANCH_NAME}"
        echo "BRANCH_NAME=${BRANCH_NAME}" >> "${GITHUB_OUTPUT}"

    - name: '⬆️ Update package versions'
      working-directory: '${{ inputs.working-directory }}'
      shell: 'bash'
      run: |
        npm run release:version "${{ inputs.release-version }}"

    - name: '💾 Commit and Conditionally Push package versions'
      working-directory: '${{ inputs.working-directory }}'
      shell: 'bash'
      env:
        BRANCH_NAME: '${{ steps.release_branch.outputs.BRANCH_NAME }}'
        DRY_RUN: '${{ inputs.dry-run }}'
        RELEASE_TAG: '${{ inputs.release-tag }}'
      run: |-
        git add package.json package-lock.json packages/*/package.json
        git commit -m "chore(release): ${RELEASE_TAG}"
        if [[ "${DRY_RUN}" == "false" ]]; then
          echo "Pushing release branch to remote..."
          git push --set-upstream origin "${BRANCH_NAME}" --follow-tags
        else
          echo "Dry run enabled. Skipping push."
        fi

    - name: '🛠️ Build and Prepare Packages'
      working-directory: '${{ inputs.working-directory }}'
      shell: 'bash'
      run: |
        npm run build:packages
        npm run prepare:package

    - name: 'Configure npm for publishing'
      uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020'
      with:
        node-version-file: '${{ inputs.working-directory }}/.nvmrc'
        registry-url: 'https://wombat-dressing-room.appspot.com'
        scope: '@google'

    - name: '📦 Publish @google/gemini-cli-core'
      working-directory: '${{ inputs.working-directory }}'
      env:
        NODE_AUTH_TOKEN: '${{ inputs.wombat-token-core }}'
      shell: 'bash'
      run: |
        npm publish \
          --dry-run="${{ inputs.dry-run }}" \
          --workspace="@google/gemini-cli-core" \
          --tag="${{ inputs.npm-tag }}"

    - name: '🔗 Install latest core package'
      working-directory: '${{ inputs.working-directory }}'
      if: "${{ inputs.dry-run == 'false' }}"
      shell: 'bash'
      run: |
        npm install "@google/gemini-cli-core@${{ inputs.release-version }}" \
        --workspace="@google/gemini-cli" \
        --save-exact

    - name: '📦 Publish @google/gemini-cli'
      working-directory: '${{ inputs.working-directory }}'
      env:
        NODE_AUTH_TOKEN: '${{ inputs.wombat-token-cli }}'
      shell: 'bash'
      run: |
        npm publish \
          --dry-run="${{ inputs.dry-run }}" \
          --workspace="@google/gemini-cli" \
          --tag="${{ inputs.npm-tag }}"

    - name: '🎁 Bundle'
      working-directory: '${{ inputs.working-directory }}'
      shell: 'bash'
      run: |
        npm run bundle

    - name: '🎉 Create GitHub Release'
      working-directory: '${{ inputs.working-directory }}'
      if: "${{ inputs.dry-run == 'false' && inputs.skip-github-release == 'false' }}"
      env:
        GITHUB_TOKEN: '${{ inputs.github-token }}'
      shell: 'bash'
      run: |
        gh release create "${{ inputs.release-tag }}" \
          bundle/gemini.js \
          --target "${{ steps.release_branch.outputs.BRANCH_NAME }}" \
          --title "Release ${{ inputs.release-tag }}" \
          --notes-start-tag "${{ inputs.previous-tag }}" \
          --generate-notes

    - name: '🧹 Clean up release branch'
      working-directory: '${{ inputs.working-directory }}'
      if: "${{ inputs.dry-run == 'false' }}"
      continue-on-error: true
      shell: 'bash'
      run: |
        echo "Cleaning up release branch ${{ steps.release_branch.outputs.BRANCH_NAME }}..."
        git push origin --delete "${{ steps.release_branch.outputs.BRANCH_NAME }}"
