name: '<PERSON><PERSON> and <PERSON><PERSON> Pull Request'
description: 'Creates a pull request and merges it automatically.'

inputs:
  branch-name:
    description: 'The name of the branch to create the PR from.'
    required: true
  pr-title:
    description: 'The title of the pull request.'
    required: true
  pr-body:
    description: 'The body of the pull request.'
    required: true
  base-branch:
    description: 'The branch to merge into.'
    required: true
    default: 'main'
  app-id:
    description: 'The ID of the GitHub App.'
    required: true
  private-key:
    description: 'The private key of the GitHub App.'
    required: true
  dry-run:
    description: 'Whether to run in dry-run mode.'
    required: false
    default: 'false'

runs:
  using: 'composite'
  steps:
    - name: 'Generate GitHub App Token'
      id: 'generate_token'
      if: "inputs.dry-run == 'false'"
      uses: 'actions/create-github-app-token@a8d616148505b5069dccd32f177bb87d7f39123b'
      with:
        app-id: '${{ inputs.app-id }}'
        private-key: '${{ inputs.private-key }}'
        permission-pull-requests: 'write'
        permission-contents: 'write'

    - name: 'Create and Approve Pull Request'
      if: "inputs.dry-run == 'false'"
      env:
        GH_TOKEN: '${{ steps.generate_token.outputs.token }}'
      shell: 'bash'
      run: |
        set -e
        PR_URL=$(gh pr create \
          --title "${{ inputs.pr-title }}" \
          --body "${{ inputs.pr-body }}" \
          --base "${{ inputs.base-branch }}" \
          --head "${{ inputs.branch-name }}" \
          --fill)
        gh pr merge "$PR_URL" --auto --squash
