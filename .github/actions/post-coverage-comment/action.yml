name: 'Post Coverage Comment Action'
description: 'Prepares and posts a code coverage comment to a PR.'

inputs:
  cli_json_file:
    description: 'Path to CLI coverage-summary.json'
    required: true
  core_json_file:
    description: 'Path to Core coverage-summary.json'
    required: true
  cli_full_text_summary_file:
    description: 'Path to CLI full-text-summary.txt'
    required: true
  core_full_text_summary_file:
    description: 'Path to Core full-text-summary.txt'
    required: true
  node_version:
    description: 'Node.js version for context in messages'
    required: true
  os:
    description: 'The os for context in messages'
    required: true
  github_token:
    description: 'GitHub token for posting comments'
    required: true

runs:
  using: 'composite'
  steps:
    - name: 'Prepare Coverage Comment'
      id: 'prep_coverage_comment'
      shell: 'bash'
      env:
        CLI_JSON_FILE: '${{ inputs.cli_json_file }}'
        CORE_JSON_FILE: '${{ inputs.core_json_file }}'
        CLI_FULL_TEXT_SUMMARY_FILE: '${{ inputs.cli_full_text_summary_file }}'
        CORE_FULL_TEXT_SUMMARY_FILE: '${{ inputs.core_full_text_summary_file }}'
        COMMENT_FILE: 'coverage-comment.md'
        NODE_VERSION: '${{ inputs.node_version }}'
        OS: '${{ inputs.os }}'
      run: |-
        # Extract percentages using jq for the main table
        if [ -f "${CLI_JSON_FILE}" ]; then
          cli_lines_pct="$(jq -r '.total.lines.pct' "${CLI_JSON_FILE}")"
          cli_statements_pct="$(jq -r '.total.statements.pct' "${CLI_JSON_FILE}")"
          cli_functions_pct="$(jq -r '.total.functions.pct' "${CLI_JSON_FILE}")"
          cli_branches_pct="$(jq -r '.total.branches.pct' "${CLI_JSON_FILE}")"
        else
          cli_lines_pct="N/A"
          cli_statements_pct="N/A"
          cli_functions_pct="N/A"
          cli_branches_pct="N/A"
          echo "CLI coverage-summary.json not found at: ${CLI_JSON_FILE}" >&2 # Error to stderr
        fi

        if [ -f "${CORE_JSON_FILE}" ]; then
          core_lines_pct="$(jq -r '.total.lines.pct' "${CORE_JSON_FILE}")"
          core_statements_pct="$(jq -r '.total.statements.pct' "${CORE_JSON_FILE}")"
          core_functions_pct="$(jq -r '.total.functions.pct' "${CORE_JSON_FILE}")"
          core_branches_pct="$(jq -r '.total.branches.pct' "${CORE_JSON_FILE}")"
        else
          core_lines_pct="N/A"
          core_statements_pct="N/A"
          core_functions_pct="N/A"
          core_branches_pct="N/A"
          echo "Core coverage-summary.json not found at: ${CORE_JSON_FILE}" >&2 # Error to stderr
        fi

        echo "## Code Coverage Summary" > "${COMMENT_FILE}"
        echo "" >> "${COMMENT_FILE}"
        echo "| Package | Lines | Statements | Functions | Branches |" >> "${COMMENT_FILE}"
        echo "|---|---|---|---|---|" >> "${COMMENT_FILE}"
        echo "| CLI | ${cli_lines_pct}% | ${cli_statements_pct}% | ${cli_functions_pct}% | ${cli_branches_pct}% |" >> "${COMMENT_FILE}"
        echo "| Core | ${core_lines_pct}% | ${core_statements_pct}% | ${core_functions_pct}% | ${core_branches_pct}% |" >> "${COMMENT_FILE}"
        echo "" >> "${COMMENT_FILE}"

        # CLI Package - Collapsible Section (with full text summary from file)
        echo "<details>" >> "${COMMENT_FILE}"
        echo "<summary>CLI Package - Full Text Report</summary>" >> "${COMMENT_FILE}"
        echo "" >> "${COMMENT_FILE}"
        echo '```text' >> "${COMMENT_FILE}"
        if [ -f "${CLI_FULL_TEXT_SUMMARY_FILE}" ]; then
          cat "${CLI_FULL_TEXT_SUMMARY_FILE}" >> "${COMMENT_FILE}"
        else
          echo "CLI full-text-summary.txt not found at: ${CLI_FULL_TEXT_SUMMARY_FILE}" >> "${COMMENT_FILE}"
        fi
        echo '```' >> "${COMMENT_FILE}"
        echo "</details>" >> "${COMMENT_FILE}"
        echo "" >> "${COMMENT_FILE}"

        # Core Package - Collapsible Section (with full text summary from file)
        echo "<details>" >> "${COMMENT_FILE}"
        echo "<summary>Core Package - Full Text Report</summary>" >> "${COMMENT_FILE}"
        echo "" >> "${COMMENT_FILE}"
        echo '```text' >> "${COMMENT_FILE}"
        if [ -f "${CORE_FULL_TEXT_SUMMARY_FILE}" ]; then
          cat "${CORE_FULL_TEXT_SUMMARY_FILE}" >> "${COMMENT_FILE}"
        else
          echo "Core full-text-summary.txt not found at: ${CORE_FULL_TEXT_SUMMARY_FILE}" >> "${COMMENT_FILE}"
        fi
        echo '```' >> "${COMMENT_FILE}"
        echo "</details>" >> "${COMMENT_FILE}"
        echo "" >> "${COMMENT_FILE}"

        echo "_For detailed HTML reports, please see the 'coverage-reports-${NODE_VERSION}-${OS}' artifact from the main CI run._" >> "${COMMENT_FILE}"

    - name: 'Post Coverage Comment'
      uses: 'thollander/actions-comment-pull-request@65f9e5c9a1f2cd378bd74b2e057c9736982a8e74' # ratchet:thollander/actions-comment-pull-request@v3
      if: |-
        ${{ always() }}
      with:
        file-path: 'coverage-comment.md' # Use the generated file directly
        comment-tag: 'code-coverage-summary'
        github-token: '${{ inputs.github_token }}'
