name: 'Testing: E2E'

on:
  push:
    branches:
      - 'main'
  # This will run for PRs from the base repository, providing secrets.
  pull_request:
    branches:
      - 'main'
      - 'release/**'
  # This will run for PRs from forks when a label is added.
  pull_request_target:
    types: ['labeled']
  merge_group:

concurrency:
  group: '${{ github.workflow }}-${{ github.head_ref || github.ref }}'
  cancel-in-progress: |-
    ${{ github.ref != 'refs/heads/main' && !startsWith(github.ref, 'refs/heads/release/') }}

jobs:
  build:
    name: 'Build Project'
    runs-on: 'gemini-cli-ubuntu-16-core'
    if: |
      github.event_name == 'push' ||
      github.event_name == 'merge_group' ||
      (github.event.pull_request.head.repo.full_name == github.repository) ||
      (github.event.label.name == 'maintainer:e2e:ok')
    steps:
      - name: 'Checkout'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8' # ratchet:actions/checkout@v5

      - name: 'Set up Node.js'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020' # ratchet:actions-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: 'Install dependencies'
        run: 'npm ci'

      - name: 'Build project'
        run: 'npm run build'

      - name: 'Archive build artifacts'
        run: 'tar -cvf build-artifacts.tar bundle/ node_modules/ packages/ package.json'

      - name: 'Upload build artifacts'
        uses: 'actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02'
        with:
          name: 'build-artifacts-${{ github.run_id }}'
          path: 'build-artifacts.tar'

  e2e_linux:
    name: 'E2E Test (Linux) - ${{ matrix.sandbox }}'
    needs:
      - 'build'
    if: |
      github.event_name == 'push' ||
      github.event_name == 'merge_group' ||
      (github.event.pull_request.head.repo.full_name == github.repository) ||
      (github.event.label.name == 'maintainer:e2e:ok')
    runs-on: 'gemini-cli-ubuntu-16-core'
    strategy:
      fail-fast: false
      matrix:
        sandbox:
          - 'sandbox:none'
          - 'sandbox:docker'
        node-version:
          - '20.x'

    steps:
      - name: 'Checkout (fork)'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8' # ratchet:actions/checkout@v5
        if: "github.event_name == 'pull_request_target'"
        with:
          ref: '${{ github.event.pull_request.head.sha }}'
          repository: '${{ github.event.pull_request.head.repo.full_name }}'

      - name: 'Checkout (internal)'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8' # ratchet:actions/checkout@v5
        if: "github.event_name != 'pull_request_target'"

      - name: 'Download build artifacts'
        uses: 'actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093'
        with:
          name: 'build-artifacts-${{ github.run_id }}'
          path: '.'

      - name: 'Extract build artifacts'
        run: 'tar -xvf build-artifacts.tar'

      - name: 'Set up Node.js ${{ matrix.node-version }}'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020' # ratchet:actions-node@v4
        with:
          node-version: '${{ matrix.node-version }}'

      - name: 'Set up Docker'
        if: "matrix.sandbox == 'sandbox:docker'"
        uses: 'docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435' # ratchet:docker/setup-buildx-action@v3

      - name: 'Run E2E tests'
        env:
          GEMINI_API_KEY: '${{ secrets.GEMINI_API_KEY }}'
          KEEP_OUTPUT: 'true'
          SANDBOX: '${{ matrix.sandbox }}'
          VERBOSE: 'true'
          GEMINI_SANDBOX: 'docker'
        shell: 'bash'
        run: |
          if [[ "${{ matrix.sandbox }}" == "sandbox:docker" ]]; then
            npm run build:sandbox
          fi
          npx vitest run --root ./integration-tests

  e2e_slow_platforms:
    name: 'Slow E2E - Mac'
    needs:
      - 'build'
    if: |
      github.event_name == 'push' ||
      github.event_name == 'merge_group' ||
      (github.event.pull_request.head.repo.full_name == github.repository) ||
      (github.event.label.name == 'maintainer:e2e:ok')
    runs-on: '${{ matrix.os }}'
    continue-on-error: true
    strategy:
      fail-fast: false
      matrix:
        os:
          - 'macos-latest'
        node-version:
          - '20.x'

    steps:
      - name: 'Checkout (fork)'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8' # ratchet:actions/checkout@v5
        if: "github.event_name == 'pull_request_target'"
        with:
          ref: '${{ github.event.pull_request.head.sha }}'
          repository: '${{ github.event.pull_request.head.repo.full_name }}'

      - name: 'Checkout (internal)'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8' # ratchet:actions/checkout@v5
        if: "github.event_name != 'pull_request_target'"

      - name: 'Download build artifacts'
        uses: 'actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093'
        with:
          name: 'build-artifacts-${{ github.run_id }}'
          path: '.'

      - name: 'Extract build artifacts'
        run: 'tar -xvf build-artifacts.tar'

      - name: 'Set up Node.js ${{ matrix.node-version }}'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020' # ratchet:actions-node@v4
        with:
          node-version: '${{ matrix.node-version }}'

      - name: 'Fix rollup optional dependencies on macOS'
        if: "runner.os == 'macOS'"
        run: |
          npm cache clean --force
          npm install --no-save @rollup/rollup-darwin-arm64 || true

      - name: 'Run E2E tests (non-Windows)'
        if: "runner.os != 'Windows'"
        env:
          GEMINI_API_KEY: '${{ secrets.GEMINI_API_KEY }}'
          KEEP_OUTPUT: 'true'
          SANDBOX: 'sandbox:none'
          VERBOSE: 'true'
        run: 'npx vitest run --root ./integration-tests'

  e2e_windows:
    name: 'Slow E2E - Win'
    if: |
      github.event_name == 'push' ||
      github.event_name == 'merge_group' ||
      (github.event.pull_request.head.repo.full_name == github.repository) ||
      (github.event.label.name == 'maintainer:e2e:ok')
    runs-on: 'gemini-cli-windows-16-core'
    continue-on-error: true

    steps:
      - name: 'Checkout (fork)'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8' # ratchet:actions/checkout@v5
        if: "github.event_name == 'pull_request_target'"
        with:
          ref: '${{ github.event.pull_request.head.sha }}'
          repository: '${{ github.event.pull_request.head.repo.full_name }}'

      - name: 'Checkout (internal)'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8' # ratchet:actions/checkout@v5
        if: "github.event_name != 'pull_request_target'"

      - name: 'Set up Node.js 20.x'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020' # ratchet:actions-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: 'Configure Windows Defender exclusions'
        run: |
          Add-MpPreference -ExclusionPath $env:GITHUB_WORKSPACE -Force
          Add-MpPreference -ExclusionPath "$env:GITHUB_WORKSPACE\node_modules" -Force
          Add-MpPreference -ExclusionPath "$env:GITHUB_WORKSPACE\packages" -Force
          Add-MpPreference -ExclusionPath "$env:TEMP" -Force
        shell: 'pwsh'

      - name: 'Configure npm for Windows performance'
        run: |
          npm config set progress false
          npm config set audit false
          npm config set fund false
          npm config set loglevel error
          npm config set maxsockets 32
          npm config set registry https://registry.npmjs.org/
        shell: 'pwsh'

      - name: 'Install dependencies'
        run: 'npm ci'
        shell: 'pwsh'

      - name: 'Run E2E tests'
        env:
          GEMINI_API_KEY: '${{ secrets.GEMINI_API_KEY }}'
          KEEP_OUTPUT: 'true'
          SANDBOX: 'sandbox:none'
          VERBOSE: 'true'
          NODE_OPTIONS: '--max-old-space-size=32768 --max-semi-space-size=256'
          UV_THREADPOOL_SIZE: '32'
          NODE_ENV: 'test'
        shell: 'pwsh'
        run: 'npx vitest run --root ./integration-tests'

  e2e:
    name: 'E2E'
    if: |
      always() && (
        github.event_name == 'push' ||
        github.event_name == 'merge_group' ||
        (github.event.pull_request.head.repo.full_name == github.repository) ||
        (github.event.label.name == 'maintainer:e2e:ok')
      )
    needs:
      - 'e2e_linux'
    runs-on: 'gemini-cli-ubuntu-16-core'
    steps:
      - name: 'Check E2E test results'
        run: |
          if [[ ${{ needs.e2e_linux.result }} != 'success' ]]; then
            echo "The required E2E test job failed."
            exit 1
          fi
          echo "All required E2E test jobs passed!"
