name: 'Release: Change Tags'

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'The package version to tag (e.g., 0.5.0-preview-2). This version must already exist on the npm registry.'
        required: true
        type: 'string'
      channel:
        description: 'The npm dist-tag to apply (e.g., latest, preview, nightly).'
        required: true
        type: 'choice'
        options:
          - 'latest'
          - 'preview'
          - 'nightly'
      ref:
        description: 'The branch, tag, or SHA to run from.'
        required: false
        type: 'string'
        default: 'main'
      dry-run:
        description: 'Whether to run in dry-run mode.'
        required: false
        type: 'boolean'
        default: true

jobs:
  change-tags:
    runs-on: 'ubuntu-latest'
    permissions:
      packages: 'write'
      issues: 'write'
    steps:
      - name: 'Checkout repository'
        uses: 'actions/checkout@v4'
        with:
          ref: '${{ github.event.inputs.ref }}'
          fetch-depth: 0

      - name: 'Setup Node.js'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020'
        with:
          node-version-file: '.nvmrc'
          registry-url: 'https://wombat-dressing-room.appspot.com'
          scope: '@google'

      - name: 'Change tag for @google/gemini-cli-core'
        if: |-
          ${{ github.event.inputs.dry-run == 'false' }}
        env:
          NODE_AUTH_TOKEN: '${{ secrets.WOMBAT_TOKEN_CORE }}'
        run: |
          npm dist-tag add @google/gemini-cli-core@${{ github.event.inputs.version }} ${{ github.event.inputs.channel }}

      - name: 'Change tag for @google/gemini-cli'
        if: |-
          ${{ github.event.inputs.dry-run == 'false' }}
        env:
          NODE_AUTH_TOKEN: '${{ secrets.WOMBAT_TOKEN_CLI }}'
        run: |
          npm dist-tag add @google/gemini-cli@${{ github.event.inputs.version }} ${{ github.event.inputs.channel }}

      - name: 'Log dry run'
        if: |-
          ${{ github.event.inputs.dry-run == 'true' }}
        run: |
          echo "Dry run: Would have added tag '${{ github.event.inputs.channel }}' to version '${{ github.event.inputs.version }}' for @google/gemini-cli and @google/gemini-cli-core."
