name: 'Release: Patch from Comment'

on:
  issue_comment:
    types: ['created']

jobs:
  slash-command:
    runs-on: 'ubuntu-latest'
    # Only run if the comment is from a human user (not automated)
    if: "github.event.comment.user.type == 'User' && github.event.comment.user.login != 'github-actions[bot]'"
    permissions:
      contents: 'write'
      pull-requests: 'write'
      actions: 'write'
    steps:
      - name: 'Checkout'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8'
        with:
          fetch-depth: 1

      - name: 'Slash Command Dispatch'
        id: 'slash_command'
        uses: 'peter-evans/slash-command-dispatch@40877f718dce0101edfc7aea2b3800cc192f9ed5'
        with:
          token: '${{ secrets.GITHUB_TOKEN }}'
          commands: 'patch'
          permission: 'write'
          issue-type: 'pull-request'

      - name: 'Get PR Status'
        id: 'pr_status'
        if: "startsWith(github.event.comment.body, '/patch')"
        env:
          GH_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        run: |
          gh pr view "${{ github.event.issue.number }}" --json mergeCommit,state > pr_status.json
          echo "MERGE_COMMIT_SHA=$(jq -r .mergeCommit.oid pr_status.json)" >> "$GITHUB_OUTPUT"
          echo "STATE=$(jq -r .state pr_status.json)" >> "$GITHUB_OUTPUT"

      - name: 'Dispatch if Merged'
        if: "steps.pr_status.outputs.STATE == 'MERGED'"
        id: 'dispatch_patch'
        uses: 'actions/github-script@00f12e3e20659f42342b1c0226afda7f7c042325'
        env:
          COMMENT_BODY: '${{ github.event.comment.body }}'
        with:
          github-token: '${{ secrets.GITHUB_TOKEN }}'
          script: |
            // Parse the comment body directly to extract channel(s)
            const commentBody = process.env.COMMENT_BODY;
            console.log('Comment body:', commentBody);

            let channels = ['stable', 'preview'];  // default to both

            // Parse different formats:
            // /patch (defaults to both)
            // /patch both
            // /patch stable
            // /patch preview
            if (commentBody.trim() === '/patch' || commentBody.trim() === '/patch both') {
              channels = ['stable', 'preview'];
            } else if (commentBody.trim() === '/patch stable') {
              channels = ['stable'];
            } else if (commentBody.trim() === '/patch preview') {
              channels = ['preview'];
            } else {
              // Fallback parsing for legacy formats
              if (commentBody.includes('channel=preview')) {
                channels = ['preview'];
              } else if (commentBody.includes('--channel preview')) {
                channels = ['preview'];
              }
            }

            console.log('Detected channels:', channels);

            const dispatchedRuns = [];

            // Dispatch workflow for each channel
            for (const channel of channels) {
              console.log(`Dispatching workflow for channel: ${channel}`);

              const response = await github.rest.actions.createWorkflowDispatch({
                owner: context.repo.owner,
                repo: context.repo.repo,
                workflow_id: 'release-patch-1-create-pr.yml',
                ref: 'main',
                inputs: {
                  commit: '${{ steps.pr_status.outputs.MERGE_COMMIT_SHA }}',
                  channel: channel,
                  original_pr: '${{ github.event.issue.number }}'
                }
              });

              dispatchedRuns.push({ channel, response });
            }

            // Wait a moment for the workflows to be created
            await new Promise(resolve => setTimeout(resolve, 3000));

            const runs = await github.rest.actions.listWorkflowRuns({
              owner: context.repo.owner,
              repo: context.repo.repo,
              workflow_id: 'release-patch-1-create-pr.yml',
              per_page: 20  // Increased to handle multiple runs
            });

            // Find the recent runs that match our trigger
            const recentRuns = runs.data.workflow_runs.filter(run =>
              run.event === 'workflow_dispatch' &&
              new Date(run.created_at) > new Date(Date.now() - 15000) // Within last 15 seconds
            ).slice(0, channels.length); // Limit to the number of channels we dispatched

            // Set outputs
            core.setOutput('dispatched_channels', channels.join(','));
            core.setOutput('dispatched_run_count', channels.length.toString());

            if (recentRuns.length > 0) {
              core.setOutput('dispatched_run_urls', recentRuns.map(r => r.html_url).join(','));
              core.setOutput('dispatched_run_ids', recentRuns.map(r => r.id).join(','));
            }

      - name: 'Comment on Failure'
        if: "startsWith(github.event.comment.body, '/patch') && steps.pr_status.outputs.STATE != 'MERGED'"
        uses: 'peter-evans/create-or-update-comment@67dcc547d311b736a8e6c5c236542148a47adc3d'
        with:
          token: '${{ secrets.GITHUB_TOKEN }}'
          issue-number: '${{ github.event.issue.number }}'
          body: |
            :x: The `/patch` command failed. This pull request must be merged before a patch can be created.

      - name: 'Final Status Comment - Success'
        if: "always() && startsWith(github.event.comment.body, '/patch') && steps.dispatch_patch.outcome == 'success' && steps.dispatch_patch.outputs.dispatched_run_urls"
        uses: 'peter-evans/create-or-update-comment@67dcc547d311b736a8e6c5c236542148a47adc3d'
        with:
          token: '${{ secrets.GITHUB_TOKEN }}'
          issue-number: '${{ github.event.issue.number }}'
          body: |
            ✅ **Patch workflow(s) dispatched successfully!**

            **📋 Details:**
            - **Channels**: `${{ steps.dispatch_patch.outputs.dispatched_channels }}`
            - **Commit**: `${{ steps.pr_status.outputs.MERGE_COMMIT_SHA }}`
            - **Workflows Created**: ${{ steps.dispatch_patch.outputs.dispatched_run_count }}

            **🔗 Track Progress:**
            - [View patch workflows](https://github.com/${{ github.repository }}/actions/workflows/release-patch-1-create-pr.yml)
            - [This workflow run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})

      - name: 'Final Status Comment - Dispatch Success (No URL)'
        if: "always() && startsWith(github.event.comment.body, '/patch') && steps.dispatch_patch.outcome == 'success' && !steps.dispatch_patch.outputs.dispatched_run_urls"
        uses: 'peter-evans/create-or-update-comment@67dcc547d311b736a8e6c5c236542148a47adc3d'
        with:
          token: '${{ secrets.GITHUB_TOKEN }}'
          issue-number: '${{ github.event.issue.number }}'
          body: |
            ✅ **Patch workflow(s) dispatched successfully!**

            **📋 Details:**
            - **Channels**: `${{ steps.dispatch_patch.outputs.dispatched_channels }}`
            - **Commit**: `${{ steps.pr_status.outputs.MERGE_COMMIT_SHA }}`
            - **Workflows Created**: ${{ steps.dispatch_patch.outputs.dispatched_run_count }}

            **🔗 Track Progress:**
            - [View patch workflows](https://github.com/${{ github.repository }}/actions/workflows/release-patch-1-create-pr.yml)
            - [This workflow run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})

      - name: 'Final Status Comment - Failure'
        if: "always() && startsWith(github.event.comment.body, '/patch') && (steps.dispatch_patch.outcome == 'failure' || steps.dispatch_patch.outcome == 'cancelled')"
        uses: 'peter-evans/create-or-update-comment@67dcc547d311b736a8e6c5c236542148a47adc3d'
        with:
          token: '${{ secrets.GITHUB_TOKEN }}'
          issue-number: '${{ github.event.issue.number }}'
          body: |
            ❌ **Patch workflow dispatch failed!**

            There was an error dispatching the patch creation workflow.

            **🔍 Troubleshooting:**
            - Check that the PR is properly merged
            - Verify workflow permissions
            - Review error logs in the workflow run

            **🔗 Debug Links:**
            - [This workflow run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
            - [Patch workflow history](https://github.com/${{ github.repository }}/actions/workflows/release-patch-1-create-pr.yml)
