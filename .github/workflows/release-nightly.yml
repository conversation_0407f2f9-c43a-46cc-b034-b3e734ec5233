name: 'Release: Nightly'

on:
  schedule:
    - cron: '0 0 * * *'
  workflow_dispatch:
    inputs:
      dry_run:
        description: 'Run a dry-run of the release process; no branches, npm packages or GitHub releases will be created.'
        required: true
        type: 'boolean'
        default: true
      force_skip_tests:
        description: 'Select to skip the "Run Tests" step in testing. Prod releases should run tests'
        required: false
        type: 'boolean'
        default: false
      ref:
        description: 'The branch, tag, or SHA to release from.'
        required: false
        type: 'string'
        default: 'main'

jobs:
  release:
    runs-on: 'ubuntu-latest'
    permissions:
      contents: 'write'
      packages: 'write'
      issues: 'write'
      pull-requests: 'write'
    steps:
      - name: 'Checkout'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8'
        with:
          ref: '${{ github.event.inputs.ref }}'
          fetch-depth: 0

      - name: 'Setup Node.js'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020' # ratchet:actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: 'Install Dependencies'
        run: 'npm ci'

      - name: 'Run Tests'
        uses: './.github/actions/run-tests'
        with:
          force_skip_tests: '${{ github.event.inputs.force_skip_tests }}'
          gemini_api_key: '${{ secrets.GEMINI_API_KEY }}'

      - name: 'Get Nightly Version'
        id: 'nightly_version'
        env:
          GH_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        run: |
          VERSION_JSON=$(node scripts/get-release-version.js --type=nightly)
          echo "RELEASE_TAG=$(echo "${VERSION_JSON}" | jq -r .releaseTag)" >> "${GITHUB_OUTPUT}"
          echo "RELEASE_VERSION=$(echo "${VERSION_JSON}" | jq -r .releaseVersion)" >> "${GITHUB_OUTPUT}"
          echo "NPM_TAG=$(echo "${VERSION_JSON}" | jq -r .npmTag)" >> "${GITHUB_OUTPUT}"
          echo "PREVIOUS_TAG=$(echo "${VERSION_JSON}" | jq -r .previousReleaseTag)" >> "${GITHUB_OUTPUT}"

      - name: 'Publish Release'
        uses: './.github/actions/publish-release'
        with:
          release-version: '${{ steps.nightly_version.outputs.RELEASE_VERSION }}'
          release-tag: '${{ steps.nightly_version.outputs.RELEASE_TAG }}'
          npm-tag: '${{ steps.nightly_version.outputs.NPM_TAG }}'
          wombat-token-core: '${{ secrets.WOMBAT_TOKEN_CORE }}'
          wombat-token-cli: '${{ secrets.WOMBAT_TOKEN_CLI }}'
          github-token: '${{ secrets.GITHUB_TOKEN }}'
          dry-run: '${{ github.event.inputs.dry_run }}'
          previous-tag: '${{ steps.nightly_version.outputs.PREVIOUS_TAG }}'

      - name: 'Create and Merge Pull Request'
        uses: './.github/actions/create-pull-request'
        with:
          branch-name: 'release/${{ steps.nightly_version.outputs.RELEASE_TAG }}'
          pr-title: 'chore(release): bump version to ${{ steps.nightly_version.outputs.RELEASE_VERSION }}'
          pr-body: 'Automated version bump for nightly release.'
          app-id: '${{ secrets.APP_ID }}'
          private-key: '${{ secrets.PRIVATE_KEY }}'
          dry-run: '${{ github.event.inputs.dry_run }}'

      - name: 'Create Issue on Failure'
        if: '${{ failure() && github.event.inputs.dry_run == false }}'
        env:
          GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
          RELEASE_TAG: '${{ steps.nightly_version.outputs.RELEASE_TAG }}'
          DETAILS_URL: '${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}'
        run: |
          gh issue create \
            --title 'Nightly Release Failed for ${RELEASE_TAG} on $(date +'%Y-%m-%d')' \
            --body 'The nightly-release workflow failed. See the full run for details: ${DETAILS_URL}' \
            --label 'kind/bug,release-failure,priority/p0'
