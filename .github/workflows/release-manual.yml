name: 'Release: Manual'

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'The version to release (e.g., v0.1.11). Must be a valid semver string with a "v" prefix.'
        required: true
        type: 'string'
      ref:
        description: 'The branch, tag, or SHA to release from.'
        required: true
        type: 'string'
      npm_channel:
        description: 'The npm channel to publish to.'
        required: true
        type: 'choice'
        options:
          - 'preview'
          - 'nightly'
          - 'latest'
          - 'dev'
        default: 'dev'
      dry_run:
        description: 'Run a dry-run of the release process; no branches, npm packages or GitHub releases will be created.'
        required: true
        type: 'boolean'
        default: true
      force_skip_tests:
        description: 'Select to skip the "Run Tests" step in testing. Prod releases should run tests'
        required: false
        type: 'boolean'
        default: false
      skip_github_release:
        description: 'Select to skip creating a GitHub release and create a npm release only.'
        required: false
        type: 'boolean'
        default: false

jobs:
  release:
    runs-on: 'ubuntu-latest'
    permissions:
      contents: 'write'
      packages: 'write'
      issues: 'write'
    steps:
      - name: 'Checkout'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8'
        with:
          ref: '${{ github.event.inputs.ref }}'
          fetch-depth: 0

      - name: 'Setup Node.js'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020'
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: 'Install Dependencies'
        run: 'npm ci'

      - name: 'Prepare Release Info'
        id: 'release_info'
        run: |
          RELEASE_VERSION="${{ github.event.inputs.version }}"
          echo "RELEASE_VERSION=${RELEASE_VERSION#v}" >> "${GITHUB_OUTPUT}"
          echo "PREVIOUS_TAG=$(git describe --tags --abbrev=0)" >> "${GITHUB_OUTPUT}"

      - name: 'Run Tests'
        if: |-
          ${{ github.event.inputs.force_skip_tests != true }}
        uses: './.github/actions/run-tests'
        with:
          gemini_api_key: '${{ secrets.GEMINI_API_KEY }}'

      - name: 'Publish Release'
        uses: './.github/actions/publish-release'
        with:
          release-version: '${{ steps.release_info.outputs.RELEASE_VERSION }}'
          release-tag: '${{ github.event.inputs.version }}'
          npm-tag: '${{ github.event.inputs.npm_channel }}'
          wombat-token-core: '${{ secrets.WOMBAT_TOKEN_CORE }}'
          wombat-token-cli: '${{ secrets.WOMBAT_TOKEN_CLI }}'
          github-token: '${{ secrets.GITHUB_TOKEN }}'
          dry-run: '${{ github.event.inputs.dry_run }}'
          previous-tag: '${{ steps.release_info.outputs.PREVIOUS_TAG }}'
          skip-github-release: '${{ github.event.inputs.skip_github_release }}'
