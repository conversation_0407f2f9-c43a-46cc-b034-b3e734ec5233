name: 'Deploy GitHub Pages'

on:
  push:
    tags: 'v*'
  workflow_dispatch:

permissions:
  contents: 'read'
  pages: 'write'
  id-token: 'write'

# Allow only one concurrent deployment, skipping runs queued between the run
# in-progress and latest queued. However, do NOT cancel in-progress runs as we
# want to allow these production deployments to complete.
concurrency:
  group: '${{ github.workflow }}'
  cancel-in-progress: false

jobs:
  build:
    if: |-
      ${{ !contains(github.ref_name, 'nightly') }}
    runs-on: 'ubuntu-latest'
    steps:
      - name: 'Checkout'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8' # ratchet:actions/checkout@v5

      - name: 'Setup Pages'
        uses: 'actions/configure-pages@983d7736d9b0ae728b81ab479565c72886d7745b' # ratchet:actions/configure-pages@v5

      - name: 'Build with <PERSON><PERSON><PERSON>'
        uses: 'actions/jekyll-build-pages@44a6e6beabd48582f863aeeb6cb2151cc1716697' # ratchet:actions/jekyll-build-pages@v1
        with:
          source: './'
          destination: './_site'

      - name: 'Upload artifact'
        uses: 'actions/upload-pages-artifact@56afc609e74202658d3ffba0e8f6dda462b719fa' # ratchet:actions/upload-pages-artifact@v3

  deploy:
    environment:
      name: 'github-pages'
      url: '${{ steps.deployment.outputs.page_url }}'
    runs-on: 'ubuntu-latest'
    needs: 'build'
    steps:
      - name: 'Deploy to GitHub Pages'
        id: 'deployment'
        uses: 'actions/deploy-pages@d6db90164ac5ed86f2b6aed7e0febac5b3c0c03e' # ratchet:actions/deploy-pages@v4
