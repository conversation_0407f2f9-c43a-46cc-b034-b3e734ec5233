name: 'Release: Patch (2) Trigger'

on:
  pull_request:
    types:
      - 'closed'
    branches:
      - 'release/**'
  workflow_dispatch:
    inputs:
      ref:
        description: 'The head ref of the merged hotfix PR to trigger the release for (e.g. hotfix/v1.2.3/cherry-pick-abc).'
        required: true
        type: 'string'
      workflow_ref:
        description: 'The ref to checkout the workflow code from.'
        required: false
        type: 'string'
        default: 'main'
      workflow_id:
        description: 'The workflow to trigger. Defaults to patch-release.yml'
        required: false
        type: 'string'
        default: 'release-patch-3-release.yml'
      dry_run:
        description: 'Whether this is a dry run.'
        required: false
        type: 'boolean'
        default: false
      force_skip_tests:
        description: 'Select to skip the "Run Tests" step in testing. Prod releases should run tests'
        required: false
        type: 'boolean'
        default: false

jobs:
  trigger-patch-release:
    if: "(github.event_name == 'pull_request' && github.event.pull_request.merged == true && startsWith(github.event.pull_request.head.ref, 'hotfix/')) || github.event_name == 'workflow_dispatch'"
    runs-on: 'ubuntu-latest'
    permissions:
      actions: 'write'
      contents: 'write'
      pull-requests: 'write'
    steps:
      - name: 'Checkout'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8'
        with:
          ref: "${{ github.event.inputs.workflow_ref || 'main' }}"
          fetch-depth: 1

      - name: 'Setup Node.js'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020'
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: 'Install Dependencies'
        run: 'npm ci'

      - name: 'Trigger Patch Release'
        env:
          GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
          HEAD_REF: "${{ github.event_name == 'pull_request' && github.event.pull_request.head.ref || github.event.inputs.ref }}"
          PR_BODY: "${{ github.event_name == 'pull_request' && github.event.pull_request.body || '' }}"
          WORKFLOW_ID: '${{ github.event.inputs.workflow_id }}'
          GITHUB_REPOSITORY_OWNER: '${{ github.repository_owner }}'
          GITHUB_REPOSITORY_NAME: '${{ github.event.repository.name }}'
          GITHUB_EVENT_NAME: '${{ github.event_name }}'
          GITHUB_EVENT_PAYLOAD: '${{ toJSON(github.event) }}'
          FORCE_SKIP_TESTS: '${{ github.event.inputs.force_skip_tests }}'
        run: |
          node scripts/releasing/patch-trigger.js
