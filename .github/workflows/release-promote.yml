name: 'Release: Promote'

on:
  workflow_dispatch:
    inputs:
      dry_run:
        description: 'Run a dry-run of the release process; no branches, npm packages or GitHub releases will be created.'
        required: true
        type: 'boolean'
        default: true
      force_skip_tests:
        description: 'Select to skip the "Run Tests" step in testing. Prod releases should run tests'
        required: false
        type: 'boolean'
        default: false
      ref:
        description: 'The branch, tag, or SHA to release from.'
        required: false
        type: 'string'
        default: 'main'
      stable_version_override:
        description: 'Manually override the stable version number.'
        required: false
        type: 'string'
      preview_version_override:
        description: 'Manually override the preview version number.'
        required: false
        type: 'string'

jobs:
  calculate-versions:
    name: 'Calculate Versions and Plan'
    runs-on: 'ubuntu-latest'
    outputs:
      STABLE_VERSION: '${{ steps.versions.outputs.STABLE_VERSION }}'
      STABLE_SHA: '${{ steps.versions.outputs.STABLE_SHA }}'
      PREVIOUS_STABLE_TAG: '${{ steps.versions.outputs.PREVIOUS_STABLE_TAG }}'
      PREVIEW_VERSION: '${{ steps.versions.outputs.PREVIEW_VERSION }}'
      PREVIEW_SHA: '${{ steps.versions.outputs.PREVIEW_SHA }}'
      PREVIOUS_PREVIEW_TAG: '${{ steps.versions.outputs.PREVIOUS_PREVIEW_TAG }}'
      NEXT_NIGHTLY_VERSION: '${{ steps.versions.outputs.NEXT_NIGHTLY_VERSION }}'
      PREVIOUS_NIGHTLY_TAG: '${{ steps.versions.outputs.PREVIOUS_NIGHTLY_TAG }}'

    steps:
      - name: 'Checkout'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8'
        with:
          fetch-depth: 0
          fetch-tags: true

      - name: 'Setup Node.js'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020'
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: 'Install Dependencies'
        run: 'npm ci'

      - name: 'Print Inputs'
        shell: 'bash'
        run: |-
          echo "${{ toJSON(inputs) }}"

      - name: 'Calculate Versions and SHAs'
        id: 'versions'
        env:
          GH_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
        run: |
          set -e
          STABLE_JSON=$(node scripts/get-release-version.js --type=stable ${{ github.event.inputs.stable_version_override && format('--stable_version_override={0}', github.event.inputs.stable_version_override) || '' }})
          PREVIEW_JSON=$(node scripts/get-release-version.js --type=preview ${{ github.event.inputs.preview_version_override && format('--preview_version_override={0}', github.event.inputs.preview_version_override) || '' }})
          NIGHTLY_JSON=$(node scripts/get-release-version.js --type=nightly)
          echo "STABLE_JSON_COMMAND=node scripts/get-release-version.js --type=stable ${{ github.event.inputs.stable_version_override && format('--stable_version_override={0}', github.event.inputs.stable_version_override) || '' }}"
          echo "PREVIEW_JSON_COMMAND=node scripts/get-release-version.js --type=preview ${{ github.event.inputs.preview_version_override && format('--preview_version_override={0}', github.event.inputs.preview_version_override) || '' }}"
          echo "NIGHTLY_JSON_COMMAND=node scripts/get-release-version.js --type=nightly"
          echo "STABLE_JSON: ${STABLE_JSON}"
          echo "PREVIEW_JSON: ${PREVIEW_JSON}"
          echo "NIGHTLY_JSON: ${NIGHTLY_JSON}"
          echo "STABLE_VERSION=$(echo "${STABLE_JSON}" | jq -r .releaseVersion)" >> "${GITHUB_OUTPUT}"
          # shellcheck disable=SC1083
          echo "STABLE_SHA=$(git rev-parse "$(echo "${PREVIEW_JSON}" | jq -r .previousReleaseTag)"^{commit})" >> "${GITHUB_OUTPUT}"
          echo "PREVIOUS_STABLE_TAG=$(echo "${STABLE_JSON}" | jq -r .previousReleaseTag)" >> "${GITHUB_OUTPUT}"
          echo "PREVIEW_VERSION=$(echo "${PREVIEW_JSON}" | jq -r .releaseVersion)" >> "${GITHUB_OUTPUT}"
          # shellcheck disable=SC1083
          echo "PREVIEW_SHA=$(git rev-parse '${{ github.event.inputs.ref }}'^{commit})" >> "${GITHUB_OUTPUT}"
          echo "PREVIOUS_PREVIEW_TAG=$(echo "${PREVIEW_JSON}" | jq -r .previousReleaseTag)" >> "${GITHUB_OUTPUT}"
          echo "NEXT_NIGHTLY_VERSION=$(echo "${NIGHTLY_JSON}" | jq -r .releaseVersion)" >> "${GITHUB_OUTPUT}"
          echo "PREVIOUS_NIGHTLY_TAG=$(echo "${NIGHTLY_JSON}" | jq -r .previousReleaseTag)" >> "${GITHUB_OUTPUT}"
          CURRENT_NIGHTLY_TAG=$(git describe --tags --abbrev=0 --match="*nightly*")
          echo "CURRENT_NIGHTLY_TAG=${CURRENT_NIGHTLY_TAG}" >> "${GITHUB_OUTPUT}"
          echo "NEXT_SHA=$(git rev-parse HEAD)" >> "${GITHUB_OUTPUT}"

      - name: 'Display Pending Updates'
        run: |
          echo "Pending Changes, Next Versions:"
          echo "  Nightly: ${{ steps.versions.outputs.NEXT_NIGHTLY_VERSION }}"
          echo "  Preview: ${{ steps.versions.outputs.PREVIEW_VERSION }}"
          echo "  Stable: ${{ steps.versions.outputs.STABLE_VERSION }}"
          echo ""
          echo "Relevant SHAs:"
          echo "  Stable: Will be cut from:  : ${{ steps.versions.outputs.STABLE_SHA }} / ${{ steps.versions.outputs.PREVIOUS_STABLE_TAG }}"
          echo "  Preview will be cut from:  : ${{ steps.versions.outputs.PREVIEW_SHA }} (${{ github.event.inputs.ref }})". Users last saw preview as: ${{ steps.versions.outputs.PREVIOUS_PREVIEW_TAG }}
          echo "  Nightly will be udpated in : ${{ steps.versions.outputs.NEXT_SHA }} (${{ github.event.inputs.ref }}). Previous nightly tag: ${{ steps.versions.outputs.PREVIOUS_NIGHTLY_TAG }}"

  test:
    name: 'Test ${{ matrix.channel }}'
    needs: 'calculate-versions'
    runs-on: 'ubuntu-latest'
    strategy:
      fail-fast: false
      matrix:
        include:
          - channel: 'stable'
            sha: '${{ needs.calculate-versions.outputs.STABLE_SHA }}'
          - channel: 'preview'
            sha: '${{ needs.calculate-versions.outputs.PREVIEW_SHA }}'
          - channel: 'nightly'
            sha: '${{ github.event.inputs.ref }}'
    steps:
      - name: 'Checkout Ref'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8'
        with:
          ref: '${{ github.event.inputs.ref }}'

      - name: 'Checkout correct SHA'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8'
        with:
          ref: '${{ matrix.sha }}'
          path: 'release'
          fetch-depth: 0

      - name: 'Setup Node.js'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020'
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: 'Install Dependencies'
        working-directory: './release'
        run: 'npm ci'

      - name: 'Run Tests'
        uses: './.github/actions/run-tests'
        with:
          force_skip_tests: '${{ github.event.inputs.force_skip_tests }}'
          gemini_api_key: '${{ secrets.GEMINI_API_KEY }}'
          working-directory: './release'

  publish-preview:
    name: 'Publish preview'
    needs: ['calculate-versions', 'test']
    runs-on: 'ubuntu-latest'
    permissions:
      contents: 'write'
      packages: 'write'
      issues: 'write'
    steps:
      - name: 'Checkout Ref'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8'
        with:
          ref: '${{ github.event.inputs.ref }}'

      - name: 'Checkout correct SHA'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8'
        with:
          ref: '${{ needs.calculate-versions.outputs.PREVIEW_SHA }}'
          path: 'release'
          fetch-depth: 0

      - name: 'Setup Node.js'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020'
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: 'Install Dependencies'
        working-directory: './release'
        run: 'npm ci'

      - name: 'Publish Release'
        uses: './.github/actions/publish-release'
        with:
          release-version: '${{ needs.calculate-versions.outputs.PREVIEW_VERSION }}'
          release-tag: 'v${{ needs.calculate-versions.outputs.PREVIEW_VERSION }}'
          npm-tag: 'preview'
          wombat-token-core: '${{ secrets.WOMBAT_TOKEN_CORE }}'
          wombat-token-cli: '${{ secrets.WOMBAT_TOKEN_CLI }}'
          github-token: '${{ secrets.GITHUB_TOKEN }}'
          dry-run: '${{ github.event.inputs.dry_run }}'
          previous-tag: '${{ needs.calculate-versions.outputs.PREVIOUS_PREVIEW_TAG }}'
          working-directory: './release'

      - name: 'Create Issue on Failure'
        if: '${{ failure() && github.event.inputs.dry_run == false }}'
        env:
          GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
          RELEASE_TAG: 'v${{ needs.calculate-versions.outputs.PREVIEW_VERSION }}'
          DETAILS_URL: '${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}'
        run: |
          gh issue create \
            --title 'Promote Release Failed for ${RELEASE_TAG} on $(date +'%Y-%m-%d')' \
            --body 'The promote-release workflow failed during preview publish. See the full run for details: ${DETAILS_URL}' \
            --label 'kind/bug,release-failure,priority/p0'

  publish-stable:
    name: 'Publish stable'
    needs: ['calculate-versions', 'test', 'publish-preview']
    runs-on: 'ubuntu-latest'
    permissions:
      contents: 'write'
      packages: 'write'
      issues: 'write'
    steps:
      - name: 'Checkout Ref'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8'
        with:
          ref: '${{ github.event.inputs.ref }}'

      - name: 'Checkout correct SHA'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8'
        with:
          ref: '${{ needs.calculate-versions.outputs.STABLE_SHA }}'
          path: 'release'
          fetch-depth: 0

      - name: 'Setup Node.js'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020'
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: 'Install Dependencies'
        working-directory: './release'
        run: 'npm ci'

      - name: 'Publish Release'
        uses: './.github/actions/publish-release'
        with:
          release-version: '${{ needs.calculate-versions.outputs.STABLE_VERSION }}'
          release-tag: 'v${{ needs.calculate-versions.outputs.STABLE_VERSION }}'
          npm-tag: 'latest'
          wombat-token-core: '${{ secrets.WOMBAT_TOKEN_CORE }}'
          wombat-token-cli: '${{ secrets.WOMBAT_TOKEN_CLI }}'
          github-token: '${{ secrets.GITHUB_TOKEN }}'
          dry-run: '${{ github.event.inputs.dry_run }}'
          previous-tag: '${{ needs.calculate-versions.outputs.PREVIOUS_STABLE_TAG }}'
          working-directory: './release'

      - name: 'Create Issue on Failure'
        if: '${{ failure() && github.event.inputs.dry_run == false }}'
        env:
          GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
          RELEASE_TAG: 'v${{ needs.calculate-versions.outputs.STABLE_VERSION }}'
          DETAILS_URL: '${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}'
        run: |
          gh issue create \
            --title 'Promote Release Failed for ${RELEASE_TAG} on $(date +'%Y-%m-%d')' \
            --body 'The promote-release workflow failed during stable publish. See the full run for details: ${DETAILS_URL}' \
            --label 'kind/bug,release-failure,priority/p0'

  nightly-pr:
    name: 'Create Nightly PR'
    needs: ['publish-stable', 'calculate-versions']
    runs-on: 'ubuntu-latest'
    permissions:
      contents: 'write'
      pull-requests: 'write'
      issues: 'write'
    steps:
      - name: 'Checkout Ref'
        uses: 'actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8'
        with:
          ref: '${{ github.event.inputs.ref }}'

      - name: 'Setup Node.js'
        uses: 'actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020'
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: 'Install Dependencies'
        run: 'npm ci'

      - name: 'Configure Git User'
        run: |-
          git config user.name "gemini-cli-robot"
          git config user.email "<EMAIL>"

      - name: 'Create and switch to a new branch'
        id: 'release_branch'
        run: |
          BRANCH_NAME="chore/nightly-version-bump-${{ needs.calculate-versions.outputs.NEXT_NIGHTLY_VERSION }}"
          git switch -c "${BRANCH_NAME}"
          echo "BRANCH_NAME=${BRANCH_NAME}" >> "${GITHUB_OUTPUT}"

      - name: 'Update package versions'
        run: 'npm run release:version "${{ needs.calculate-versions.outputs.NEXT_NIGHTLY_VERSION }}"'

      - name: 'Commit and Push package versions'
        env:
          BRANCH_NAME: '${{ steps.release_branch.outputs.BRANCH_NAME }}'
          DRY_RUN: '${{ github.event.inputs.dry_run }}'
        run: |-
          git add package.json packages/*/package.json
          if [ -f package-lock.json ]; then
            git add package-lock.json
          fi
          git commit -m "chore(release): bump version to ${{ needs.calculate-versions.outputs.NEXT_NIGHTLY_VERSION }}"
          if [[ "${DRY_RUN}" == "false" ]]; then
            echo "Pushing release branch to remote..."
            git push --set-upstream origin "${BRANCH_NAME}"
          else
            echo "Dry run enabled. Skipping push."
          fi

      - name: 'Create and Merge Pull Request'
        uses: './.github/actions/create-pull-request'
        with:
          branch-name: '${{ steps.release_branch.outputs.BRANCH_NAME }}'
          pr-title: 'chore(release): bump version to ${{ needs.calculate-versions.outputs.NEXT_NIGHTLY_VERSION }}'
          pr-body: 'Automated version bump to prepare for the next nightly release.'
          app-id: '${{ secrets.APP_ID }}'
          private-key: '${{ secrets.PRIVATE_KEY }}'
          dry-run: '${{ github.event.inputs.dry_run }}'

      - name: 'Create Issue on Failure'
        if: '${{ failure() && github.event.inputs.dry_run == false }}'
        env:
          GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
          RELEASE_TAG: 'v${{ needs.calculate-versions.outputs.NEXT_NIGHTLY_VERSION }}'
          DETAILS_URL: '${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}'
        run: |
          gh issue create \
            --title 'Promote Release Failed for ${RELEASE_TAG} on $(date +'%Y-%m-%d')' \
            --body 'The promote-release workflow failed during nightly PR creation. See the full run for details: ${DETAILS_URL}' \
            --label 'kind/bug,release-failure,priority/p0'
