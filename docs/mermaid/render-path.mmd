graph TD
    %% --- Style Definitions ---
    classDef new fill:#98fb98,color:#000
    classDef changed fill:#add8e6,color:#000
    classDef unchanged fill:#f0f0f0,color:#000
    classDef dispatcher fill:#f9e79f,color:#000,stroke:#333,stroke-width:1px
    classDef container fill:#f5f5f5,color:#000,stroke:#ccc

    %% --- Component Tree ---
    subgraph "Entry Point"
      A["gemini.tsx"]
    end

    subgraph "State & Logic Wrapper"
      B["AppContainer.tsx"]
    end

    subgraph "Primary Layout"
      C["App.tsx"]
    end

    A -.-> B
    B -.-> C

    subgraph "UI Containers"
        direction LR
        C -.-> D["MainContent"]
        C -.-> G["Composer"]
        C -.-> F["DialogManager"]
        C -.-> E["Notifications"]
    end

    subgraph "MainContent"
        direction TB
        D -.-> H["AppHeader"]
        D -.-> I["HistoryItemDisplay"]:::dispatcher
        D -.-> L["ShowMoreLines"]
    end

    subgraph "Composer"
        direction TB
        G -.-> K_Prompt["InputPrompt"]
        G -.-> <PERSON>_Footer["Footer"]
    end

    subgraph "DialogManager"
        F -.-> J["Various Dialogs<br>(Auth, Theme, Settings, etc.)"]
    end

    %% --- Apply Styles ---
    class B,D,E,F,G,H,J,K_Prompt,L new
    class A,C,I changed
    class K_Footer unchanged

    %% --- Link Styles ---
    %% MainContent Branch (Blue)
    linkStyle 2,6,7,8 stroke:#64b5f6,stroke-width:2px
    %% Composer Branch (Green)
    linkStyle 3,9,10 stroke:#81c784,stroke-width:2px
    %% DialogManager Branch (Orange)
    linkStyle 4,11 stroke:#ffb74d,stroke-width:2px
    %% Notifications Branch (Violet)
    linkStyle 5 stroke:#ba68c8,stroke-width:2px

