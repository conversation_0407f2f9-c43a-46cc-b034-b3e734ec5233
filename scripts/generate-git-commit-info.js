/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import { execSync } from 'node:child_process';
import { existsSync, mkdirSync, writeFileSync } from 'node:fs';
import { dirname, join, relative } from 'node:path';
import { fileURLToPath } from 'node:url';
import { readPackageUp } from 'read-package-up';

const __dirname = dirname(fileURLToPath(import.meta.url));
const root = join(__dirname, '..');
const scriptPath = relative(root, fileURLToPath(import.meta.url));
const generatedCliDir = join(root, 'packages/cli/src/generated');
const cliGitCommitFile = join(generatedCliDir, 'git-commit.ts');
const generatedCoreDir = join(root, 'packages/core/src/generated');
const coreGitCommitFile = join(generatedCoreDir, 'git-commit.ts');
let gitCommitInfo = 'N/A';
let cliVersion = 'UNKNOWN';

if (!existsSync(generatedCliDir)) {
  mkdirSync(generatedCliDir, { recursive: true });
}

if (!existsSync(generatedCoreDir)) {
  mkdirSync(generatedCoreDir, { recursive: true });
}

try {
  const gitHash = execSync('git rev-parse --short HEAD', {
    encoding: 'utf-8',
  }).trim();
  if (gitHash) {
    gitCommitInfo = gitHash;
  }

  const result = await readPackageUp();
  cliVersion = result?.packageJson?.version ?? 'UNKNOWN';
} catch {
  // ignore
}

const fileContent = `/**
 * @license
 * Copyright ${new Date().getFullYear()} Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

// This file is auto-generated by the build script (${scriptPath})
// Do not edit this file manually.
export const GIT_COMMIT_INFO = '${gitCommitInfo}';
export const CLI_VERSION = '${cliVersion}';
`;

writeFileSync(cliGitCommitFile, fileContent);
writeFileSync(coreGitCommitFile, fileContent);
