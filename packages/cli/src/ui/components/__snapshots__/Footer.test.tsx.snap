// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`<Footer /> > footer configuration filtering (golden snapshots) > renders complete footer in narrow terminal (baseline narrow) > complete-footer-narrow 1`] = `
"long (main*)

no sandbox (see /docs)

gemini-pro (100% context left)"
`;

exports[`<Footer /> > footer configuration filtering (golden snapshots) > renders complete footer with all sections visible (baseline) > complete-footer-wide 1`] = `
"...bar/and/some/more/directories/to/make/it/long    no sandbox (see     gemini-pro (100% context
(main*)                                             /docs)             left)"
`;

exports[`<Footer /> > footer configuration filtering (golden snapshots) > renders footer with CWD and model info hidden to test alignment (only sandbox visible) > footer-only-sandbox 1`] = `"                                       no sandbox (see /docs)"`;

exports[`<Footer /> > footer configuration filtering (golden snapshots) > renders footer with all optional sections hidden (minimal footer) > footer-minimal 1`] = `""`;

exports[`<Footer /> > footer configuration filtering (golden snapshots) > renders footer with only model info hidden (partial filtering) > footer-no-model 1`] = `"...bar/and/some/more/directories/to/make/it/long (main*)                     no sandbox (see /docs)"`;
