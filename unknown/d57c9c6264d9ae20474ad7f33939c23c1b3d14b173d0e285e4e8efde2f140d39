{"compilerOptions": {"strict": true, "esModuleInterop": true, "skipLibCheck": true, "noImplicitAny": true, "noImplicitOverride": true, "noImplicitReturns": true, "noImplicitThis": true, "forceConsistentCasingInFileNames": true, "noPropertyAccessFromIndexSignature": true, "noUnusedLocals": true, "strictBindCallApply": true, "strictFunctionTypes": true, "strictNullChecks": true, "strictPropertyInitialization": true, "resolveJsonModule": true, "sourceMap": true, "composite": true, "incremental": true, "declaration": true, "allowSyntheticDefaultImports": true, "verbatimModuleSyntax": true, "lib": ["ES2023"], "module": "NodeNext", "moduleResolution": "nodenext", "target": "es2022", "types": ["node", "vitest/globals"], "jsx": "react-jsx"}}